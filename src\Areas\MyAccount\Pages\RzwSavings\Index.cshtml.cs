using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Models;

namespace RazeWinComTr.Areas.MyAccount.Pages.RzwSavings;

public class IndexModel : PageModel
{
    private readonly IRzwSavingsService _rzwSavingsService;
    private readonly IRzwWalletBalanceManagementService _rzwBalanceService;
    private readonly IRzwSavingsPlanService _rzwPlanService;
    private readonly IRzwSavingsInterestService _rzwInterestService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public IndexModel(
        IRzwSavingsService rzwSavingsService,
        IRzwWalletBalanceManagementService rzwBalanceService,
        IRzwSavingsPlanService rzwPlanService,
        IRzwSavingsInterestService rzwInterestService,
        IStringLocalizer<SharedResource> localizer)
    {
        _rzwSavingsService = rzwSavingsService;
        _rzwBalanceService = rzwBalanceService;
        _rzwPlanService = rzwPlanService;
        _rzwInterestService = rzwInterestService;
        _localizer = localizer;
    }

    // Dashboard Data
    public RzwSavingsDashboard Dashboard { get; set; } = new();
    public List<RzwSavingsAccountDisplayModel> ActiveAccounts { get; set; } = new();
    public List<RzwSavingsAccountDisplayModel> InactiveAccounts { get; set; } = new();
    public List<RzwSavingsPlan> AvailablePlans { get; set; } = new();
    public RzwBalanceInfo? UserRzwBalance { get; set; }

    // View Options
    [BindProperty(SupportsGet = true)]
    public bool ShowInactive { get; set; } = false;

    // Computed Properties
    public bool HasActiveAccounts => ActiveAccounts.Any();
    public bool HasInactiveAccounts => InactiveAccounts.Any();
    public bool HasAvailableBalance => UserRzwBalance?.AvailableRzw > 0;
    public string WelcomeMessage { get; set; } = string.Empty;

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        try
        {
            // Get user's RZW balance
            UserRzwBalance = await _rzwBalanceService.GetRzwBalanceInfoAsync(userId.Value);

            // Get active savings accounts and convert to display models
            var activeSavingsAccounts = await _rzwSavingsService.GetUserActiveSavingsAsync(userId.Value);
            ActiveAccounts = activeSavingsAccounts.Select(account => new RzwSavingsAccountDisplayModel
            {
                Id = account.Id,
                RzwAmount = account.RzwAmount,
                MaturityDate = account.MaturityDate,
                StartDate = account.StartDate,
                IsMatured = account.IsMaturedForDisplay,
                DaysRemaining = account.DaysRemainingForDisplay,
                TotalDays = account.TotalDaysForDisplay,
                ProgressPercentage = account.ProgressPercentageForDisplay,
                // Additional plan details
                EarlyWithdrawalPenalty = account.EarlyWithdrawalPenalty,
                AutoRenew = account.AutoRenew,
                TotalEarnedRzw = account.TotalEarnedRzw,
                TermType = account.TermType,
                TermDuration = account.TermDuration,
                InterestRate = account.InterestRate,
                // Use optimized approach: stored values for matured accounts, calculated for active accounts
                CurrentEarnedInterest = account.Status == RzwSavingsStatus.Matured ?
                    account.TotalEarnedRzw : // Use stored value for matured accounts
                    (account.Plan != null ? RzwSavingsCalculationHelper.CalculateCurrentEarnedInterest(account.RzwAmount, account.Plan, account.StartDate, account.MaturityDate) : 0),
                ProjectedTotalInterest = account.Status == RzwSavingsStatus.Matured ?
                    account.TotalEarnedRzw : // Use stored value for matured accounts
                    (account.Plan != null ? RzwSavingsCalculationHelper.CalculateProjectedTotalInterest(account.RzwAmount, account.Plan) : 0),
                ProjectedMaturityAmount = account.Status == RzwSavingsStatus.Matured ?
                    (account.FinalMaturityAmount ?? account.RzwAmount + account.TotalEarnedRzw) : // Use stored value for matured accounts
                    (account.Plan != null ? RzwSavingsCalculationHelper.CalculateProjectedMaturityAmount(account.RzwAmount, account.Plan) : account.RzwAmount),
                // Generate localized term display text
                TermDisplayText = RzwSavingsDisplayHelper.GetLocalizedTermDisplayText(_localizer, account.TermType, account.TermDuration),
                PlanName = account.Plan?.Name ?? string.Empty,
                Plan = account.Plan,
                PlanDescription = account.Plan?.Description ?? string.Empty,
            }).ToList();

            // Get inactive accounts if requested
            if (ShowInactive)
            {
                var allUserSavingsAccounts = await _rzwSavingsService.GetUserAllSavingsAsync(userId.Value);
                var inactiveAccounts = allUserSavingsAccounts.Where(a => a.Status != RzwSavingsStatus.Active).ToList();
                InactiveAccounts = inactiveAccounts.Select(account => new RzwSavingsAccountDisplayModel
                {
                    Id = account.Id,
                    RzwAmount = account.RzwAmount,
                    MaturityDate = account.MaturityDate,
                    StartDate = account.StartDate,
                    IsMatured = account.IsMaturedForDisplay,
                    DaysRemaining = account.DaysRemainingForDisplay,
                    TotalDays = account.TotalDaysForDisplay,
                    ProgressPercentage = account.ProgressPercentageForDisplay,
                    // Additional plan details
                    EarlyWithdrawalPenalty = account.EarlyWithdrawalPenalty,
                    AutoRenew = account.AutoRenew,
                    TotalEarnedRzw = account.TotalEarnedRzw,
                    TermType = account.TermType,
                    TermDuration = account.TermDuration,
                    InterestRate = account.InterestRate,
                    Status = account.Status,
                    // Use optimized approach: stored values for matured accounts, calculated for active accounts
                    CurrentEarnedInterest = account.Status == RzwSavingsStatus.Matured ?
                        account.TotalEarnedRzw : // Use stored value for matured accounts
                        (account.Plan != null ? RzwSavingsCalculationHelper.CalculateCurrentEarnedInterest(account.RzwAmount, account.Plan, account.StartDate, account.MaturityDate) : 0),
                    ProjectedTotalInterest = account.Status == RzwSavingsStatus.Matured ?
                        account.TotalEarnedRzw : // Use stored value for matured accounts
                        (account.Plan != null ? RzwSavingsCalculationHelper.CalculateProjectedTotalInterest(account.RzwAmount, account.Plan) : 0),
                    ProjectedMaturityAmount = account.Status == RzwSavingsStatus.Matured ?
                        (account.FinalMaturityAmount ?? account.RzwAmount + account.TotalEarnedRzw) : // Use stored value for matured accounts
                        (account.Plan != null ? RzwSavingsCalculationHelper.CalculateProjectedMaturityAmount(account.RzwAmount, account.Plan) : account.RzwAmount),
                    // Generate localized term display text
                    TermDisplayText = RzwSavingsDisplayHelper.GetLocalizedTermDisplayText(_localizer, account.TermType, account.TermDuration),
                    PlanName = account.Plan?.Name ?? string.Empty,
                    Plan = account.Plan,
                    PlanDescription = account.Plan?.Description ?? string.Empty,
                }).ToList();
            }

            // Get available plans
            AvailablePlans = await _rzwPlanService.GetActivePlansAsync();

            // Calculate dashboard statistics
            await CalculateDashboardAsync(userId.Value);

            // Set welcome message
            WelcomeMessage = HasActiveAccounts
                ? _localizer["Welcome back! Here are your active savings accounts."].Value
                : _localizer["Start earning interest on your RZW tokens with our savings plans."].Value;
        }
        catch (Exception)
        {
            // Handle errors gracefully
            UserRzwBalance = null;
            ActiveAccounts = new List<RzwSavingsAccountDisplayModel>();
            AvailablePlans = new List<RzwSavingsPlan>();
            WelcomeMessage = _localizer["Unable to load savings data. Please try again later."].Value;
        }

        return Page();
    }

    private async Task CalculateDashboardAsync(int userId)
    {
        try
        {
            // Calculate total investment (sum of all active accounts)
            Dashboard.TotalInvestment = ActiveAccounts.Sum(a => a.RzwAmount);

            // Calculate total interest earned from ALL accounts (including matured ones)
            // Use TotalEarnedRzw from accounts as the authoritative source
            var allUserAccounts = await _rzwSavingsService.GetUserAllSavingsAsync(userId);
            Dashboard.TotalInterestEarned = allUserAccounts.Sum(a => a.TotalEarnedRzw);

            // Calculate projected monthly earnings (based on current active accounts)
            Dashboard.ProjectedMonthlyEarnings = CalculateProjectedMonthlyEarnings();

            // Set account count
            Dashboard.ActiveAccountsCount = ActiveAccounts.Count;
        }
        catch (Exception)
        {
            // Set default values if calculation fails
            Dashboard = new RzwSavingsDashboard();
        }
    }

    private decimal CalculateProjectedMonthlyEarnings()
    {
        decimal monthlyEarnings = 0;

        foreach (var account in ActiveAccounts)
        {
            // Skip accounts without a plan
            if (account.Plan == null) continue;

            // Calculate daily interest using helper
            var dailyInterest = RzwSavingsCalculationHelper.CalculateDailyInterest(account.RzwAmount, account.Plan);

            // Convert to monthly (30 days)
            monthlyEarnings += dailyInterest * 30;
        }

        return monthlyEarnings;
    }
}

/// <summary>
/// Dashboard summary data for RZW Savings
/// </summary>
public class RzwSavingsDashboard
{
    public decimal TotalInvestment { get; set; }
    public decimal TotalInterestEarned { get; set; }
    public decimal ProjectedMonthlyEarnings { get; set; }
    public int ActiveAccountsCount { get; set; }

    public bool HasInvestments => TotalInvestment > 0;
    public bool HasEarnings => TotalInterestEarned > 0;
}

/// <summary>
/// Display model for RZW Savings Account with pre-calculated UI properties
/// </summary>
public class RzwSavingsAccountDisplayModel
{
    public int Id { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public decimal RzwAmount { get; set; }
    public DateTime MaturityDate { get; set; }
    public DateTime StartDate { get; set; }
    public RzwSavingsPlan? Plan { get; set; }

    // Pre-calculated display properties (computed in code-behind)
    public bool IsMatured { get; set; }
    public int DaysRemaining { get; set; }
    public int TotalDays { get; set; }
    public double ProgressPercentage { get; set; }

    // Additional plan details for enhanced display
    public decimal EarlyWithdrawalPenalty { get; set; }
    public bool AutoRenew { get; set; }
    public decimal TotalEarnedRzw { get; set; }
    public string TermType { get; set; } = string.Empty;
    public int TermDuration { get; set; }
    public decimal InterestRate { get; set; }
    public string PlanDescription { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;

    // New properties for current and projected earnings
    public decimal CurrentEarnedInterest { get; set; }
    public decimal ProjectedTotalInterest { get; set; }
    public decimal ProjectedMaturityAmount { get; set; }

    // Computed display properties for the new fields
    public string EarlyWithdrawalPenaltyDisplayText => $"{(EarlyWithdrawalPenalty * 100).ToString("N1")}%";
    public string AutoRenewDisplayText => AutoRenew ? "Enabled" : "Disabled";

    // Localized term display text (set during model creation)
    public string TermDisplayText { get; set; } = string.Empty;
}


