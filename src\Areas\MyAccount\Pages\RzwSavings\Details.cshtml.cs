using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using System.Text.Json;

namespace RazeWinComTr.Areas.MyAccount.Pages.RzwSavings;

public class DetailsModel : PageModel
{
    private readonly AppDbContext _context;
    private readonly IRzwSavingsService _rzwSavingsService;
    private readonly IRzwSavingsInterestService _rzwInterestService;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<DetailsModel> _logger;

    public DetailsModel(
        AppDbContext context,
        IRzwSavingsService rzwSavingsService,
        IRzwSavingsInterestService rzwInterestService,
        IStringLocalizer<SharedResource> localizer,
        ILogger<DetailsModel> logger)
    {
        _context = context;
        _rzwSavingsService = rzwSavingsService;
        _rzwInterestService = rzwInterestService;
        _localizer = localizer;
        _logger = logger;
    }

    public RzwSavingsAccount? SavingsAccount { get; set; }
    public List<RzwSavingsInterestPayment> InterestHistory { get; set; } = new();
    public SavingsAccountSummary Summary { get; set; } = new();

    [BindProperty]
    public string AutoRenewAction { get; set; } = "";

    public SweetAlert2Message? AlertMessage { get; set; }

    // AlertMessage'ı TempData ile taşı
    [TempData]
    public string? AlertMessageJson { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        // TempData'dan AlertMessage'ı deserialize et
        if (!string.IsNullOrEmpty(AlertMessageJson))
        {
            try { AlertMessage = JsonSerializer.Deserialize<SweetAlert2Message>(AlertMessageJson); } catch { }
            AlertMessageJson = null; // bir kere gösterilsin
        }

        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Get savings account with ownership validation
        SavingsAccount = await _rzwSavingsService.GetSavingsAccountAsync(id, userId.Value);
        if (SavingsAccount == null)
        {
            // Log unauthorized access attempt
            var logger = HttpContext.RequestServices.GetRequiredService<ILogger<DetailsModel>>();
            logger.LogWarning("Unauthorized access attempt to savings account {AccountId} by user {UserId}", id, userId.Value);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["You don't have permission to view this savings account or it doesn't exist."],
                Icon = "error"
            };
            return RedirectToPage("./Index");
        }

        // Get interest history for this account
        InterestHistory = await _context.RzwSavingsInterestPayments
            .Where(p => p.RzwSavingsAccountId == id)
            .OrderByDescending(p => p.PaymentDate)
            .ToListAsync();

        // Calculate summary
        CalculateSummary();

        return Page();
    }

    public async Task<IActionResult> OnPostEarlyWithdrawAsync(int id)
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        try
        {
            var result = await _rzwSavingsService.EarlyWithdrawAsync(id, userId.Value);
            
            if (result.Success)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Success"],
                    Text = _localizer["Early withdrawal completed successfully. Amount withdrawn: {0} RZW", result.WithdrawnAmount],
                    Icon = "success",
                    RedirectUrl = Url.Page("/MyAccount/RzwSavings/Index")
                };
                // Sayfada kal, bilgi göster
                await OnGetAsync(id); // Model'i güncelle
                return Page();
            }
            else
            {
                var alert = new SweetAlert2Message
                {
                    Title = _localizer["Error"],
                    Text = result.Message,
                    Icon = "error"
                };
                AlertMessageJson = JsonSerializer.Serialize(alert);
                return RedirectToPage(new { id });
            }
        }
        catch (Exception)
        {
            var alert = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["An error occurred during early withdrawal. Please try again."],
                Icon = "error"
            };
            AlertMessageJson = JsonSerializer.Serialize(alert);
            return RedirectToPage(new { id });
        }
    }

    public async Task<IActionResult> OnPostClaimInterestAsync(int id)
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Validate ownership and maturity
        var account = await _rzwSavingsService.GetSavingsAccountAsync(id, userId.Value);
        if (account == null)
        {
            var alert = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["You don't have permission to claim interest for this account or it doesn't exist."],
                Icon = "error"
            };
            AlertMessageJson = JsonSerializer.Serialize(alert);
            return RedirectToPage(new { id });
        }
        if (account.Status != RzwSavingsStatus.Active || DateTime.UtcNow < account.MaturityDate)
        {
            var alert = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["Interest can only be claimed for matured and active accounts."],
                Icon = "error"
            };
            AlertMessageJson = JsonSerializer.Serialize(alert);
            return RedirectToPage(new { id });
        }

        try
        {
            var result = await _rzwSavingsService.ProcessMaturityWithInterestAndAutoRenewAsync(id);
            if (result.Success)
            {
                var alert = new SweetAlert2Message
                {
                    Title = _localizer["Success"],
                    Text = _localizer["Interest claimed successfully. Your principal and interest have been transferred to your balance."],
                    Icon = "success"
                };
                AlertMessageJson = JsonSerializer.Serialize(alert);
                return RedirectToPage(new { id });
            }
            else
            {
                var alert = new SweetAlert2Message
                {
                    Title = _localizer["Error"],
                    Text = result.Message,
                    Icon = "error"
                };
                AlertMessageJson = JsonSerializer.Serialize(alert);
                return RedirectToPage(new { id });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error claiming interest for savings account {AccountId}", id);
            var alert = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["An error occurred while claiming interest. Please try again."],
                Icon = "error"
            };
            AlertMessageJson = JsonSerializer.Serialize(alert);
            return RedirectToPage(new { id });
        }
    }

    public async Task<IActionResult> OnPostUpdateAutoRenewAsync(int id)
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            var alert = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["You are not authorized to perform this action."],
                Icon = "error"
            };
            AlertMessageJson = JsonSerializer.Serialize(alert);
            return Unauthorized();
        }
        // Hesap gerçekten bu kullanıcıya mı ait?
        var account = await _rzwSavingsService.GetSavingsAccountAsync(id, userId.Value);
        if (account == null)
        {
            var alert = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["You don't have permission to update this savings account or it doesn't exist."],
                Icon = "error"
            };
            AlertMessageJson = JsonSerializer.Serialize(alert);
            return RedirectToPage(new { id });
        }
        bool autoRenewValue = AutoRenewAction == "enable";
        var result = await _rzwSavingsService.UpdateAutoRenewAsync(id, autoRenewValue);
        var alertResult = new SweetAlert2Message
        {
            Title = result.Success ? _localizer["Success"] : _localizer["Error"],
            Text = result.Message,
            Icon = result.Success ? "success" : "error",
            RedirectUrl = Url.Page("/MyAccount/RzwSavings/Details", new { id })
        };
        AlertMessageJson = JsonSerializer.Serialize(alertResult);
        return RedirectToPage(new { id });
    }

    private void CalculateSummary()
    {
        if (SavingsAccount == null) return;

        var now = DateTime.UtcNow;
        var totalDays = (SavingsAccount.MaturityDate - SavingsAccount.StartDate).Days;
        var elapsedDays = Math.Max(0, (now - SavingsAccount.StartDate).Days);
        var remainingDays = Math.Max(0, (SavingsAccount.MaturityDate - now).Days);

        Summary = new SavingsAccountSummary
        {
            TotalDays = totalDays,
            ElapsedDays = elapsedDays,
            RemainingDays = remainingDays,
            ProgressPercentage = (double)RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(SavingsAccount.StartDate, SavingsAccount.MaturityDate, now),
            IsMatured = now >= SavingsAccount.MaturityDate,
            TotalInterestEarned = InterestHistory.Sum(h => h.RzwAmount),
            LastInterestPayment = InterestHistory.OrderByDescending(h => h.PaymentDate).FirstOrDefault(),
            NextInterestPayment = Summary.IsMatured ? null : now.Date.AddDays(1),
            CanWithdrawEarly = SavingsAccount.Status == RzwSavingsStatus.Active && !Summary.IsMatured
        };

        // Calculate projected final amount
        if (!Summary.IsMatured)
        {
            // Use helper for consistent compound interest calculation
            Summary.ProjectedFinalAmount = RzwSavingsCalculationHelper.CalculateFinalAmount(
                SavingsAccount.RzwAmount,
                SavingsAccount.InterestRate,
                totalDays);

            // Check for potential data integrity issues
            if (Summary.ProjectedFinalAmount == SavingsAccount.RzwAmount && SavingsAccount.InterestRate == 0)
            {
                _logger.LogWarning("Savings account {AccountId} has zero interest rate - projected final amount equals principal",
                    SavingsAccount.Id);

                // Set a flag to show warning in UI
                ViewData["HasZeroInterestRate"] = true;
                ViewData["ZeroInterestWarning"] = _localizer["This savings account has a zero interest rate. Please contact support if this is unexpected."].Value;
            }
        }
        else
        {
            // Use optimized approach for projected final amount
            if (SavingsAccount.Status == RzwSavingsStatus.Matured)
            {
                // For matured accounts, use stored final maturity amount if available
                Summary.ProjectedFinalAmount = SavingsAccount.FinalMaturityAmount ??
                                             (SavingsAccount.RzwAmount + SavingsAccount.TotalEarnedRzw);
            }
            else
            {
                // For active accounts, calculate projected amount
                if (Summary.TotalInterestEarned == 0 && SavingsAccount.Plan != null)
                {
                    var theoreticalInterest = RzwSavingsCalculationHelper.CalculateTotalInterest(
                        SavingsAccount.RzwAmount,
                        SavingsAccount.Plan,
                        SavingsAccount.TermDuration
                    );
                    Summary.ProjectedFinalAmount = SavingsAccount.RzwAmount + theoreticalInterest;
                }
                else
                {
                    Summary.ProjectedFinalAmount = SavingsAccount.RzwAmount + Summary.TotalInterestEarned;
                }
            }
        }
    }
}

/// <summary>
/// Summary information for a savings account
/// </summary>
public class SavingsAccountSummary
{
    public int TotalDays { get; set; }
    public int ElapsedDays { get; set; }
    public int RemainingDays { get; set; }
    public double ProgressPercentage { get; set; }
    public bool IsMatured { get; set; }
    public decimal TotalInterestEarned { get; set; }
    public RzwSavingsInterestPayment? LastInterestPayment { get; set; }
    public DateTime? NextInterestPayment { get; set; }
    public bool CanWithdrawEarly { get; set; }
    public decimal ProjectedFinalAmount { get; set; }
    
    // Computed properties
    public string StatusText => IsMatured ? "Matured" : "Active";
    public string StatusBadgeClass => IsMatured ? "badge-success" : "badge-warning";
}
